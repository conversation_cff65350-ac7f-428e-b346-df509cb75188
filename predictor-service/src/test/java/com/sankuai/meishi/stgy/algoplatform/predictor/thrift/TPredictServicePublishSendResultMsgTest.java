package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import com.sankuai.meishi.stgy.algoplatform.predictor.TestCaseHelper;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.BmlMatchMafkaBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import com.meituan.mafka.client.producer.IProducerProcessor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtils.class, BmlMatchMafkaBeanConfig.class, RaptorTrack.class})
public class TPredictServicePublishSendResultMsgTest {

    @Mock
    private IProducerProcessor predictResultProducer;

    @InjectMocks
    private TPredictServicePublish service = new TPredictServicePublish();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(SpringContextUtils.class);
        PowerMockito.mockStatic(BmlMatchMafkaBeanConfig.class);
        PowerMockito.mockStatic(RaptorTrack.class);
    }

    private Method getSendResultMsgMethod() throws NoSuchMethodException {
        return TestCaseHelper.getPrivateMethodForTest(
                TPredictServicePublish.class,
                "sendResultMsg",
                new Class[]{Map.class, String.class}
        );
    }

    @Test
    public void test_sendResultMsg_useAutowiredProducer_whenResultSendCellNamePresent() throws Exception {
        // given
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "any-cell");
        String message = "hello-msg";

        // stub autowired producer
        when(predictResultProducer.sendMessage(anyString())).thenReturn(null);

        // when
        Method m = getSendResultMsgMethod();
        m.invoke(service, extra, message);

        // then
        verify(predictResultProducer, times(1)).sendMessage(message);
        // 静态方法不应被调用
        PowerMockito.verifyStatic(BmlMatchMafkaBeanConfig.class, never());
        BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(anyString());
    }

    @Test
    public void test_sendResultMsg_useBeanProcessor_whenResultSendCellNameMissing_andProcessorNotNull() throws Exception {
        // given
        Map<String, String> extra = new HashMap<>(); // 未设置 resultSendCellName
        String message = "hello-msg-2";
        String beanName = "mock-bean-1";

        // stub 静态方法返回 beanName
        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null)).thenReturn(beanName);

        // stub SpringContextUtils 返回一个 Processor 实例
        IProducerProcessor processor = mock(IProducerProcessor.class);
        when(SpringContextUtils.getBean(beanName)).thenReturn(processor);
        when(processor.sendMessage(anyString())).thenReturn(null);

        // when
        Method m = getSendResultMsgMethod();
        m.invoke(service, extra, message);

        // then
        verify(processor, times(1)).sendMessage(message);
        // 不应上报空指针埋点
        PowerMockito.verifyStatic(RaptorTrack.class, never());
        RaptorTrack.Sys_UnexpectedVisitNum.report(anyString());
    }

    @Test
    public void test_sendResultMsg_reportWhenProcessorNull_andThrowNPE() throws Exception {
        // given
        Map<String, String> extra = new HashMap<>();
        String message = "hello-msg-3";
        String beanName = "mock-bean-null";

        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null)).thenReturn(beanName);
        when(SpringContextUtils.getBean(beanName)).thenReturn(null);

        Method m = getSendResultMsgMethod();

        try {
            // when: 将会因对 null 调用 sendMessage 抛出 NPE，被反射包装为 InvocationTargetException
            m.invoke(service, extra, message);
        } catch (InvocationTargetException e) {
            // then: 先校验埋点已上报
            PowerMockito.verifyStatic(RaptorTrack.class, times(1));
            RaptorTrack.Sys_UnexpectedVisitNum.report("NullProducer#" + beanName);
            // 再断言真实原因是 NPE
            assert e.getTargetException() instanceof NullPointerException;
            return;
        }
        throw new AssertionError("Expected InvocationTargetException due to NullPointerException");
    }
}

