package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.BmlMatchMafkaBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * TPredictServicePublish.sendResultMsg 私有方法的单元测试
 * 
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtils.class, BmlMatchMafkaBeanConfig.class, RaptorTrack.class})
public class TPredictServicePublishSendResultMsgTest {

    @InjectMocks
    private TPredictServicePublish tPredictServicePublish;

    @Mock
    private IProducerProcessor predictResultProducer;

    @Mock
    private IProducerProcessor mockProcessor;

    private Method sendResultMsgMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // 获取私有方法
        sendResultMsgMethod = TPredictServicePublish.class.getDeclaredMethod(
                "sendResultMsg", Map.class, String.class);
        sendResultMsgMethod.setAccessible(true);
        
        // Mock 静态类
        PowerMockito.mockStatic(SpringContextUtils.class);
        PowerMockito.mockStatic(BmlMatchMafkaBeanConfig.class);
        PowerMockito.mockStatic(RaptorTrack.class);
    }

    /**
     * 测试场景1：extra 包含 resultSendCellName 且不为空
     * 预期：使用 predictResultProducer 发送消息
     */
    @Test
    public void testSendResultMsg_WithValidResultSendCellName() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "test-cell");
        String message = "test message";

        // 执行测试
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);

        // 验证结果
        verify(predictResultProducer, times(1)).sendMessage(message);
        // 验证不会调用其他静态方法
        PowerMockito.verifyStatic(BmlMatchMafkaBeanConfig.class, never());
        BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(anyString());
    }

    /**
     * 测试场景2：extra 为空 Map
     * 预期：通过 BmlMatchMafkaBeanConfig 获取 bean 并发送消息
     */
    @Test
    public void testSendResultMsg_WithEmptyExtra() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        String message = "test message";
        String expectedBeanName = "producer-null";

        // Mock 静态方法调用
        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null))
                .thenReturn(expectedBeanName);
        when(SpringContextUtils.getBean(expectedBeanName))
                .thenReturn(mockProcessor);

        // 执行测试
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);

        // 验证结果
        verify(mockProcessor, times(1)).sendMessage(message);
        PowerMockito.verifyStatic(BmlMatchMafkaBeanConfig.class, times(1));
        BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null);
        PowerMockito.verifyStatic(SpringContextUtils.class, times(1));
        SpringContextUtils.getBean(expectedBeanName);
    }

    /**
     * 测试场景3：extra 包含 resultSendCellName 但值为空字符串
     * 预期：通过 BmlMatchMafkaBeanConfig 获取 bean 并发送消息
     */
    @Test
    public void testSendResultMsg_WithEmptyResultSendCellName() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "");
        String message = "test message";
        String expectedBeanName = "producer-";

        // Mock 静态方法调用
        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(""))
                .thenReturn(expectedBeanName);
        when(SpringContextUtils.getBean(expectedBeanName))
                .thenReturn(mockProcessor);

        // 执行测试
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);

        // 验证结果
        verify(mockProcessor, times(1)).sendMessage(message);
        PowerMockito.verifyStatic(BmlMatchMafkaBeanConfig.class, times(1));
        BmlMatchMafkaBeanConfig.getMafkaProducerBeanName("");
    }

    /**
     * 测试场景4：extra 包含 resultSendCellName 但值为 null
     * 预期：通过 BmlMatchMafkaBeanConfig 获取 bean 并发送消息
     */
    @Test
    public void testSendResultMsg_WithNullResultSendCellName() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", null);
        String message = "test message";
        String expectedBeanName = "producer-null";

        // Mock 静态方法调用
        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null))
                .thenReturn(expectedBeanName);
        when(SpringContextUtils.getBean(expectedBeanName))
                .thenReturn(mockProcessor);

        // 执行测试
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);

        // 验证结果
        verify(mockProcessor, times(1)).sendMessage(message);
        PowerMockito.verifyStatic(BmlMatchMafkaBeanConfig.class, times(1));
        BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null);
    }

    /**
     * 测试场景5：SpringContextUtils.getBean 返回 null
     * 预期：调用 RaptorTrack 报告错误，但仍然尝试调用 processor.sendMessage（会抛出 NPE）
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_WithNullProcessor() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        String message = "test message";
        String expectedBeanName = "producer-null";

        // Mock 静态方法调用
        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null))
                .thenReturn(expectedBeanName);
        when(SpringContextUtils.getBean(expectedBeanName))
                .thenReturn(null);

        // Mock RaptorTrack.Sys_UnexpectedVisitNum
        RaptorTrack.Item mockItem = mock(RaptorTrack.Item.class);
        PowerMockito.field(RaptorTrack.class, "Sys_UnexpectedVisitNum").set(null, mockItem);

        // 执行测试 - 预期会抛出 NullPointerException
        try {
            sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
        } catch (Exception e) {
            // 验证 RaptorTrack 被调用
            verify(mockItem, times(1)).report("NullProducer#" + expectedBeanName);
            throw e;
        }
    }

    /**
     * 测试场景6：extra 为 null
     * 预期：会抛出 NullPointerException，因为代码会尝试调用 extra.get()
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_WithNullExtra() throws Exception {
        // 准备测试数据
        Map<String, String> extra = null;
        String message = "test message";

        // 执行测试 - 预期抛出 NullPointerException
        sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
    }

    /**
     * 测试场景7：processor.sendMessage 抛出异常
     * 预期：异常会被传播
     */
    @Test(expected = Exception.class)
    public void testSendResultMsg_ProcessorThrowsException() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        extra.put("resultSendCellName", "test-cell");
        String message = "test message";

        // Mock processor 抛出异常
        doThrow(new RuntimeException("Send failed")).when(predictResultProducer).sendMessage(anyString());

        // 执行测试 - 预期抛出异常
        try {
            sendResultMsgMethod.invoke(tPredictServicePublish, extra, message);
        } catch (Exception e) {
            // 检查是否是预期的异常
            if (e.getCause() instanceof RuntimeException) {
                throw (Exception) e.getCause();
            }
            throw e;
        }
    }
}
