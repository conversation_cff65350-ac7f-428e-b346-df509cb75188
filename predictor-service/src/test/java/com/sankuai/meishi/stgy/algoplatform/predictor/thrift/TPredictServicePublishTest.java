package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.ConfigRepository;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.meishi.stgy.algoplatform.predictor.TestCaseHelper;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictAppService;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictContext;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.PredictionQueryAppService;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TPredictResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TScriptRequest;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.TScriptResponse;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.BmlMatchMafkaBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.mq.DzPredictorResultProducer;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SpringContextUtils.class, BmlMatchMafkaBeanConfig.class, RaptorTrack.class})
public class TPredictServicePublishTest {

    @Mock
    private PredictionQueryAppService predictionQueryAppService;

    @Mock
    private PredictAppService predictAppService;

    @Mock
    private IProducerProcessor predictResultProducer;

    @Mock
    private DzPredictorResultProducer dzPredictorResultProducer;

    @InjectMocks
    private TPredictServicePublish servicePublish = new TPredictServicePublish();

    @Mock
    private ConfigRepository configRepository;

    public TPredictServicePublishTest() {
        MockitoAnnotations.initMocks(this);
    }

    private TPredictRequest createRequestWithValidData() {
        TPredictRequest req = new TPredictRequest();
        req.setBizCode("testBizCode");
        Map<String, String> requestMap = new HashMap<>();
        // Assuming this is a valid JSON array string
        requestMap.put("mt_deals", "[{\"dealId\":\"123\"}]");
        req.setReq(requestMap);
        return req;
    }

    /**
     * 测试 queryScript 方法，正常情况
     */
    @Test
    public void testQueryScriptNormal() throws TException {
        // arrange
        TScriptRequest request = new TScriptRequest();
        List<String> bizCodes = Arrays.asList("code1", "code2");
        request.setBizCodes(bizCodes);
        Map<String, String> expectedScriptMap = new HashMap<>();
        expectedScriptMap.put("code1", "script1");
        expectedScriptMap.put("code2", "script2");
        when(predictionQueryAppService.queryScript(bizCodes)).thenReturn(expectedScriptMap);
        // act
        TScriptResponse response = servicePublish.queryScript(request);
        // assert
        Assert.assertNotNull(response);
        Assert.assertEquals(expectedScriptMap, response.getScriptMap());
    }

    /**
     * 测试 queryScript 方法，当 predictionQueryAppService.queryScript 抛出异常时
     */
    @Test(expected = TException.class)
    public void testQueryScriptWithException() throws TException {
        // arrange
        TScriptRequest request = new TScriptRequest();
        List<String> bizCodes = Arrays.asList("code1", "code2");
        request.setBizCodes(bizCodes);
        when(predictionQueryAppService.queryScript(bizCodes)).thenThrow(new RuntimeException("Service exception"));
        // act
        servicePublish.queryScript(request);
    }

    @Test
    public void testPredict() throws TException {
        PredictContext context = new PredictContext();
        context.setResp(new HashMap<>());
        context.setRespExtra(new HashMap<>());
//        when(predictAppService.predict(context)).thenReturn(context);
        TPredictRequest req = new TPredictRequest();
        TPredictResponse response = servicePublish.predict(req);
        assert response.getData() != null;
    }


    @Test(expected = Exception.class)
    public void testDzPredictWithPreprocess_WithNonExistentBizCode() throws Throwable {
        TPredictRequest req = createRequestWithValidData();
        servicePublish.dzPredictWithPreprocess(req);
        verify(configRepository, times(1)).getMap(anyString(), any(), any());
    }

    @Test
    public void buildDPlusShopTest() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Method buildDPlusShop = TestCaseHelper.getPrivateMethodForTest(TPredictServicePublish.class, "buildDPlusShop", new Class[]{TPredictRequest.class});
        TPredictRequest tPredictRequest = new TPredictRequest();
        List<Map<String, Object>> djDeals = new ArrayList<>();
        Map<String, Object> deal = new HashMap<>();
        deal.put("dj_poi_id","123");
        deal.put("mt_poi_id","456");
        deal.put("dj_deal_id","789");
        djDeals.add(deal);
        Map<String, String> req = new HashMap<>();
        req.put("dj_deals", JSONObject.toJSONString(djDeals));
        tPredictRequest.setReq(req);

        Object invoke = buildDPlusShop.invoke(servicePublish, new Object[]{tPredictRequest});
        Assert.assertNotNull(invoke);
    }

    @Test(expected = Exception.class)
    public void predictAsync() throws Exception {
        TPredictRequest req = createRequestWithValidData();
        servicePublish.predictAsync(req);
    }

    @Test
    public void testSendResultMsg_WithNullProcessor() throws Exception {
        // 准备测试数据
        Map<String, String> extra = new HashMap<>();
        String message = "test message";

        // 模拟SpringContextUtils.getBean方法
        PowerMockito.mockStatic(SpringContextUtils.class);
        PowerMockito.mockStatic(BmlMatchMafkaBeanConfig.class);
        PowerMockito.mockStatic(RaptorTrack.class);

        // 模拟BmlMatchMafkaBeanConfig.getMafkaProducerBeanName方法
        when(BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(null)).thenReturn("producer-null");

        // 模拟SpringContextUtils.getBean方法返回null
        when(SpringContextUtils.getBean("producer-null")).thenReturn(null);

        // 获取私有方法
        Method sendResultMsg = TestCaseHelper.getPrivateMethodForTest(
                TPredictServicePublish.class,
                "sendResultMsg",
                new Class[]{Map.class, String.class});

        // 执行方法
        sendResultMsg.invoke(servicePublish, extra, message);

        // 验证RaptorTrack.Sys_UnexpectedVisitNum.report被调用
        PowerMockito.verifyStatic(RaptorTrack.class, times(1));
        RaptorTrack.Sys_UnexpectedVisitNum.report("NullProducer#producer-null");
    }
}
