package com.sankuai.meishi.stgy.algoplatform.predictor.thrift;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServerExtConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.algorithm.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.client.*;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.BmlMatchMafkaBeanConfig;
import com.sankuai.meishi.stgy.algoplatform.predictor.config.LionKeys;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.DcDataReporter;
import com.sankuai.meishi.stgy.algoplatform.predictor.monitor.RaptorTrack;
import com.sankuai.meishi.stgy.algoplatform.predictor.mq.DzPredictorResultProducer;
import com.sankuai.meishi.stgy.algoplatform.predictor.python.PythonInterpreterFactory;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.DateUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.MD5Util;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.RetryUtil;
import com.sankuai.meishi.stgy.algoplatform.predictor.util.SpringContextUtils;
import com.sankuai.wenchang.infer.service.entity.DPlusProduct;
import com.sankuai.wenchang.infer.service.entity.DPlusShop;
import com.sankuai.wenchang.infer.utils.GsonCasual;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TBase;
import org.apache.thrift.TException;
import org.apache.thrift.TSerializer;
import org.apache.thrift.protocol.TSimpleJSONProtocol;
import org.codehaus.groovy.util.ListHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@MdpThriftServer(port = 9001)
@MdpThriftServerExtConfig(enableAuthHandler = true)
public class TPredictServicePublish implements TPredictService.Iface {
    private static OneLimiter oneLimiter = Rhino.newOneLimiter();
    @Resource
    private PredictAppService predictAppService;
    @Resource
    private PredictionQueryAppService predictionQueryAppService;

    @Autowired
    @Qualifier("predictResultProducer")
    private IProducerProcessor predictResultProducer;

    @Autowired
    private DzPredictorResultProducer dzPredictorResultProducer;

    Gson gson = new Gson();

    @Override
    public TPredictResponse predict(TPredictRequest req) throws TException {
        // 业务限流
        LimitResult limitResult = oneLimiter.run("PredictService.predict", ImmutableMap.of(
                "bizCode", Optional.ofNullable(req.getBizCode()).orElse("unknown")));
        if (limitResult.isReject()) {
            throw new TException(String.format("predict service degrade: bizCode:%s", req.getBizCode()));
        }

        long t0 = System.currentTimeMillis();
        TPredictResponse resp = new TPredictResponse(0);
        try {
            PredictContext context = PredictContext.newInstance(req.getBizCode(), req.getAbtestKey(), req.getReq(), req.getExtra());
            predictAppService.predict(context);

            resp.setData(transformRespData(context.getResp()));
            resp.setExtra(context.getRespExtra());
            DcDataReporter.reportMatchResult(req, JSON.toJSONString(resp), true);
            return resp;
        } catch (Exception e) {
            log.error("predict error: req:{}, ex:", serialize(req), e);
            resp.setCode(-1);
            resp.setMessage(e.getMessage());
            DcDataReporter.reportMatchResult(req, JSON.toJSONString(resp), false);
            throw new TException(String.format("predict error: req:%s ex:%s", serialize(req), e.getMessage()));
        } finally {
            log.info("predict: bizCode:{}, abtestKey:{}, req:{}, code:{}, cost:{}",
                    req.getBizCode(), req.getAbtestKey(), JSONObject.toJSONString(req.getReq()),
                    resp.getCode(), System.currentTimeMillis() - t0);
            if (log.isDebugEnabled()) {
                log.debug("predict: req:{}, resp:{}", serialize(req), serialize(resp));
            }
        }
    }

    private static Map<String, String> transformRespData(Map<String, ?> arg) {
        if (MapUtils.isEmpty(arg)) {
            return Collections.emptyMap();
        }
        Map<String, String> res = new ListHashMap<>();
        arg.forEach((k, v) -> {
            String newVal;
            if (v == null) {
                newVal = null;
            } else if (v instanceof String || v instanceof Number || v instanceof Boolean) {
                newVal = v.toString();
            } else if (v instanceof JSON) {
                // Java端使用fastjson作为解析框架
                newVal = ((JSON) v).toJSONString();
            } else {
                newVal = JSONObject.toJSONString(v);
            }
            res.put(k, newVal);
        });
        return res;
    }

    @Override
    public TPredictionResponse queryPredictions(TPredictionRequest req) throws TException {
        // 业务限流
        LimitResult limitResult = oneLimiter.run("PredictService.queryPredictions", ImmutableMap.of(
                "bizCode", Optional.ofNullable(req.getBizCode()).orElse("unknown")));
        if (limitResult.isReject()) {
            throw new TException(String.format("queryPredictions service degrade: bizCode:%s", req.getBizCode()));
        }
        TPredictionResponse resp = new TPredictionResponse(0);
        if (req.getEntityIdsSize() >= 100) {
            resp.setCode(400);
            resp.setMessage("EntityIds大小超过限制");
            return resp;
        }
        long t0 = System.currentTimeMillis();
        try {
            PredictionQueryContext context = PredictionQueryContext.newInstance(req.getBizCode(), req.getEntityIds(), req.getExtra());
            predictionQueryAppService.queryPredictions(context);

            resp.setData(Maps.transformValues(context.getData(), m -> m.stream().map(a -> {
                TPredictionValue r = new TPredictionValue();
                r.setValues(a.getValues());
                return r;
            }).collect(Collectors.toList())));
            resp.setExtra(context.getRespExtra());
            return resp;
        } catch (Exception e) {
            log.error("queryPredictions error: req:{}, ex:", serialize(req), e);
            throw new TException(String.format("queryPredictions error: req:%s ex:%s", serialize(req), e.getMessage()));
        } finally {
            log.info("queryPredictions: bizCode:{}, req:{}, code:{}, cost:{}",
                    req.getBizCode(), JSONObject.toJSONString(req.getEntityIds()),
                    resp.getCode(), System.currentTimeMillis() - t0);
            if (log.isDebugEnabled()) {
                log.debug("queryPredictions: req:{}, resp:{}", serialize(req), serialize(resp));
            }
        }
    }

    @Override
    public TScriptResponse queryScript(TScriptRequest req) throws TException {
        TScriptResponse resp = new TScriptResponse(0);
        try {
            Map<String, String> scriptMap = predictionQueryAppService.queryScript(req.getBizCodes());
            resp.setScriptMap(scriptMap);
        } catch (Exception e) {
            log.error("queryScript error: bizCodes: {}", req.getBizCodes(), e);
            throw new TException(String.format("queryScript error: bizCodes:%s, ex:%s", req.getBizCodes(), e.getMessage()));
        }
        return resp;
    }

    @Override
    public TInvokeDirectlyResponse invokeDirectly(TInvokeDirectlyRequest req) throws TException {
        long t0 = System.currentTimeMillis();
        String version = req.getPath().split("\\.")[1];
        AlgoPackage algoPackage = new AlgoPackage();
        algoPackage.setVersion(version);
        algoPackage.pullCode();
        String resp = PythonInterpreterFactory.getRuntime(req.getInterpreter())
                .getInstance()
                .invoke(req.getPath(), req.getMethod(), req.getData(), "");
        TInvokeDirectlyResponse response = new TInvokeDirectlyResponse();
        response.setCost(System.currentTimeMillis() - t0);
        response.setData(resp);
        return response;
    }

    private static String serialize(TBase<?, ?> arg) {
        try {
            return new TSerializer(new TSimpleJSONProtocol.Factory())
                    .toString(arg, "UTF-8");
        } catch (TException e) {
            log.error("serialize error: {}, ex", arg, e);
            return null;
        }
    }

    /**
     * 异步处理
     *
     * @param req
     * @throws Exception
     */
    public void predictAsync(TPredictRequest req) throws Exception {
        long t0 = System.currentTimeMillis();
        int maxRetryCount = Lion.getConfigRepository().getIntValue(LionKeys.PREDICT_ASYNC_MAX_RETRY_COUNT, 2);

        try {
            RetryUtil.retry(
                    () -> {
                        TPredictResponse resp = new TPredictResponse(0);
                        PredictContext context = PredictContext.newInstance(req.getBizCode(), req.getAbtestKey(), req.getReq(), req.getExtra());
                        predictAppService.predict(context);

                        resp.setData(transformRespData(context.getResp()));

                        resp.setExtra(context.getRespExtra());

                        String message = buildMessageByResponse(resp, req);
                        sendResultMsg(req.getExtra(), message);
                        DcDataReporter.reportMatchResult(req, message, true);
                    },
                    ex -> ex != null,
                    maxRetryCount, 500L);
        } catch (Exception e) {
            log.error("predictAsync error: req:{}, ex:", serialize(req), e);
            TPredictResponse resp = new TPredictResponse(-1);
            //报错请求没有cost信息，添上。
            Map<String, String> extra = new HashMap<>();
            extra.put("cost", String.valueOf(System.currentTimeMillis() - t0));
            resp.setExtra(extra);
            String message = buildMessageByResponse(resp, req);
            Cat.logEvent("predictAsync", "error");
            sendResultMsg(req.getExtra(), message);
            DcDataReporter.reportMatchResult(req, message, false);
        } finally {
            log.info("predictAsync: bizCode:{}, abtestKey:{}, req:{}, cost:{}",
                    req.getBizCode(), req.getAbtestKey(), JSONObject.toJSONString(req.getReq()),
                    System.currentTimeMillis() - t0);
        }
    }

    private void sendResultMsg(Map<String, String> extra, String message) throws Exception {
        if (MapUtils.isNotEmpty(extra) && StringUtils.isNotEmpty(extra.get("resultSendCellName"))) {
            predictResultProducer.sendMessage(message);
        } else {
            String cellName = extra.get("resultSendCellName");
            String beanName = BmlMatchMafkaBeanConfig.getMafkaProducerBeanName(cellName);
            IProducerProcessor processor = (IProducerProcessor) SpringContextUtils.getBean(beanName);
            if (processor == null) {
                RaptorTrack.Sys_UnexpectedVisitNum.report("NullProducer#" + beanName);
            }
            processor.sendMessage(message);
        }
    }

    private String buildMessageByResponse(TPredictResponse resp, TPredictRequest req) {
        // 放额外信息的map
        if (resp.getExtra() == null) {
            resp.setExtra(new HashMap<>());
        }

        Map<String, String> reqExtra = req.getExtra();
        if (MapUtils.isNotEmpty(reqExtra)) {
            resp.getExtra().putAll(reqExtra);
        }

        //再补上poiId
        String mtDealsJson = req.getReq().get("mt_deals");
        if (StringUtils.isNotBlank(mtDealsJson)) {
            //说明是deal匹配的请求，需要把poiID返回
            JSONArray jsonArray = JSON.parseArray(mtDealsJson);
            if (!jsonArray.isEmpty()) {
                JSONObject mtDeal = jsonArray.getJSONObject(0);
                String mtPoiId = mtDeal.getString("mt_poi_id");
                if (StringUtils.isNotBlank(mtPoiId)) {
                    resp.getExtra().put("mt_poi_id", mtPoiId);
                }

                String secondCateName = mtDeal.getString("mt_second_cate_name");
                if (StringUtils.isNotBlank(secondCateName)) {
                    resp.getExtra().put("mt_second_cate_name", secondCateName);
                }
                if (!resp.getExtra().containsKey("unique_key")) {
                    String uniqueKey = MD5Util.md5(JSON.toJSONString(req.getReq()));
                    resp.getExtra().put("unique_key", uniqueKey);
                }
            }
        }

        //新增额外信息
        String jsonString = JSON.toJSONString(resp);
        JSONObject jsonObject = JSON.parseObject(jsonString);
        String date = DateUtil.formatDate(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS_FORMAT);

        String partitionDate = "";
        if (MapUtils.isNotEmpty(reqExtra)) {
            partitionDate = reqExtra.get("partition_date");
        }
        jsonObject.put("partition_date", StringUtils.isBlank(partitionDate) ? date : partitionDate);

        jsonObject.put("match_time", date);
        jsonObject.put("match_time_str", date);
        return JSON.toJSONString(jsonObject);
    }

    public void dzPredictWithPreprocess(TPredictRequest req) throws Exception {
        PredictContext context = PredictContext.newInstance(req.getBizCode(), req.getAbtestKey(), req.getReq(), req.getExtra());
        Map<String, List> relationMap = Lion.getConfigRepository().getMap(LionKeys.PREDICT_BIZCODE_RELATION, List.class, new HashMap<>());
        List relBizCodes = relationMap.get(req.getBizCode());
        List<PredictContext> contexts = Lists.newArrayList(context);
        if (!CollectionUtils.isEmpty(relBizCodes)) {
            for (Object relBizCode : relBizCodes) {
                PredictContext predictContext = PredictContext.newInstance(relBizCode.toString(), req.getAbtestKey(), req.getReq(), req.getExtra());
                contexts.add(predictContext);
            }
        }
        dzPredictAsync(req, contexts, (x, y) -> buildMessageByResponse(x, y));
    }

    private String buildMessageByResponse(List<TPredictResponse> responses, List<PredictContext> contexts) {
        Map<String, TPredictResponse> result = new HashMap<>();
        for (int i = 0; i < contexts.size(); i++) {
            TPredictResponse tPredictResponse = responses.get(i);
            PredictContext predictContext = contexts.get(i);
            result.put(predictContext.getBizCode(), tPredictResponse);
        }

        return GsonCasual.toJson(result);
    }

    public void dzPredictAsync(TPredictRequest req, List<PredictContext> contexts, BiFunction<List<TPredictResponse>, List<PredictContext>, String> buildMessageFunction) throws Exception {
        // 业务限流
        LimitResult limitResult = oneLimiter.run("PredictService.predictAsync", ImmutableMap.of(
                "bizCode", Optional.ofNullable(req.getBizCode()).orElse("unknown")));
        if (limitResult.isReject()) {
            throw new TException(String.format("predictAsync service degrade: bizCode:%s", req.getBizCode()));
        }

        long t0 = System.currentTimeMillis();
        int maxRetryCount = Lion.getConfigRepository().getIntValue(LionKeys.PREDICT_ASYNC_MAX_RETRY_COUNT, 2);
        try {
            RetryUtil.retry(
                    () -> {
                        List<TPredictResponse> respList = new ArrayList<>();
                        for (PredictContext context : contexts) {
                            TPredictResponse resp = new TPredictResponse(0);
                            predictAppService.predict(context);

                            resp.setData(transformRespData(context.getResp()));

                            DPlusShop dPlusShop = buildDPlusShop(req);
                            Map<String, String> reqExtra = context.getReqExtra();
                            context.getRespExtra().put("dPlusShop", gson.toJson(dPlusShop));
                            context.getRespExtra().putAll(reqExtra);
                            resp.setExtra(context.getRespExtra());
                            respList.add(resp);
                        }

                        String message = buildMessageFunction.apply(respList, contexts);
                        dzPredictorResultProducer.send(message);
                    },
                    ex -> ex != null,
                    maxRetryCount, 500L);
        } catch (Exception e) {
            log.error("predictAsync error: req:{}, ex:", serialize(req), e);
            TPredictResponse resp = new TPredictResponse(-1);
            String message = buildMessageByResponse(resp, req);
            Cat.logEvent("predictAsync", "error");
            dzPredictorResultProducer.send(message);
        } finally {
            log.info("predictAsync: bizCode:{}, abtestKey:{}, req:{}, cost:{}",
                    req.getBizCode(), req.getAbtestKey(), JSONObject.toJSONString(req.getReq()),
                    System.currentTimeMillis() - t0);
        }
    }

    private DPlusShop buildDPlusShop(TPredictRequest tPredictRequest) {
        Map<String, String> req = tPredictRequest.getReq();
        String dj_deals = req.get("dj_deals");
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> djDeals = gson.fromJson(dj_deals, type);
        DPlusShop dPlusShop = new DPlusShop();
        String dj_poi_id = djDeals.get(0).get("dj_poi_id").toString();
        String mt_poi_id = djDeals.get(0).get("mt_poi_id").toString();
        dPlusShop.setDPlusPoiId(dj_poi_id);
        dPlusShop.setMtShopId(Double.valueOf(mt_poi_id).longValue());
        List<DPlusProduct> products = new ArrayList<>();
        for (Map<String, Object> djDeal : djDeals) {
            DPlusProduct dPlusProduct = new DPlusProduct();
            String dj_deal_id = djDeal.get("dj_deal_id").toString();
            dPlusProduct.setProductId(dj_deal_id);
            products.add(dPlusProduct);
        }
        dPlusShop.setProducts(products);
        return dPlusShop;
    }
}
