# JavaBridge pigeonInvoke 和 thriftInvoke 方法单元测试

## 测试文件说明

### 1. 主要测试文件
- **JavaBridgeTest.java**: 原有的测试文件，已添加了针对 `pigeonInvoke` 和 `thriftInvoke` 方法的基础测试
- **JavaBridgeInvokeTest.java**: 专门针对这两个方法的详细测试文件

### 2. 测试覆盖范围

#### pigeonInvoke 方法测试用例：
1. **正常调用成功** - 测试正常的服务调用流程
2. **JSON格式错误** - 测试服务信息JSON解析异常
3. **创建代理失败** - 测试PigeonServiceFactory创建代理失败的情况
4. **服务调用失败** - 测试GenericService调用异常
5. **空参数列表** - 测试无参数方法调用
6. **null参数** - 测试各种null参数的边界情况
7. **参数不匹配** - 测试参数类型和值数量不匹配的情况
8. **返回null结果** - 测试服务返回null的情况

#### thriftInvoke 方法测试用例：
1. **正常调用成功** - 测试正常的Thrift服务调用流程
2. **JSON格式错误** - 测试服务信息JSON解析异常
3. **创建代理失败** - 测试ThriftClientProxyBeanConfig创建代理失败
4. **获取服务对象失败** - 测试ThriftClientProxy.getObject()失败
5. **服务调用失败** - 测试GenericService调用异常
6. **空参数列表** - 测试无参数方法调用
7. **null参数** - 测试各种null参数的边界情况
8. **参数不匹配** - 测试参数类型和值数量不匹配的情况
9. **返回null结果** - 测试服务返回null的情况

### 3. 测试技术栈
- **JUnit 4**: 测试框架
- **Mockito**: Mock框架，用于模拟依赖对象
- **PowerMock**: 用于模拟静态方法（SpringContextUtils）
- **FastJSON**: JSON序列化/反序列化

### 4. 运行测试

#### 运行单个测试类：
```bash
# 运行原有测试文件中的相关测试
mvn test -Dtest=JavaBridgeTest#testPigeonInvoke_Success
mvn test -Dtest=JavaBridgeTest#testThriftInvoke_Success

# 运行专门的测试文件
mvn test -Dtest=JavaBridgeInvokeTest
```

#### 运行所有相关测试：
```bash
# 运行所有JavaBridge相关测试
mvn test -Dtest=JavaBridge*Test

# 或者使用提供的脚本
chmod +x src/main/profiles/test/run_invoke_tests.sh
./src/main/profiles/test/run_invoke_tests.sh
```

### 5. 测试数据示例

#### PigeonServiceInfo JSON示例：
```json
{
  "appKey": "com.sankuai.test",
  "interfaceName": "com.test.Service",
  "methodName": "testMethod",
  "timeout": 5000
}
```

#### ThriftServiceInfo JSON示例：
```json
{
  "appKey": "com.sankuai.test",
  "interfaceName": "com.test.ThriftService",
  "methodName": "testMethod",
  "timeOut": 5000
}
```

### 6. 注意事项

1. **Mock依赖**: 测试中使用了大量的Mock对象来模拟真实的服务调用环境
2. **异常处理**: 重点测试了各种异常情况下的处理逻辑
3. **边界条件**: 包含了null参数、空参数、参数不匹配等边界情况
4. **静态方法Mock**: 使用PowerMock来模拟SpringContextUtils.getBean()静态方法

### 7. 扩展建议

如果需要添加更多测试用例，可以考虑：
1. 测试不同的参数类型组合
2. 测试超时场景
3. 测试并发调用
4. 集成测试（需要真实的服务环境）

### 8. 测试报告

运行测试后，可以通过以下命令生成测试报告：
```bash
mvn surefire-report:report
```
报告将生成在 `target/site/surefire-report.html`
