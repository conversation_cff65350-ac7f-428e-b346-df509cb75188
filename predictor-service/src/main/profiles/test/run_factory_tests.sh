#!/bin/bash

# PigeonServiceFactory 和 ThriftClientProxyBeanConfig 单元测试运行脚本
# 使用方法: ./run_factory_tests.sh

echo "开始运行 PigeonServiceFactory 和 ThriftClientProxyBeanConfig 的单元测试..."

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/../../../.." && pwd)
echo "项目根目录: $PROJECT_ROOT"

# 进入项目根目录
cd "$PROJECT_ROOT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}========================================${NC}"
echo -e "${YELLOW}  运行 PigeonServiceFactory 测试${NC}"
echo -e "${YELLOW}========================================${NC}"

# 首先尝试运行简化版本测试（不使用PowerMock）
echo "运行 PigeonServiceFactory 简化测试（推荐）..."
mvn test -Dtest=PigeonServiceFactorySimpleTest -q

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ PigeonServiceFactory 简化测试通过${NC}"
else
    echo -e "${RED}❌ PigeonServiceFactory 简化测试失败，尝试完整测试...${NC}"

    # 如果简化测试失败，尝试完整测试
    echo "运行 PigeonServiceFactory 完整测试..."
    mvn test -Dtest=PigeonServiceFactoryTest -q

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ PigeonServiceFactory 完整测试通过${NC}"
    else
        echo -e "${RED}❌ PigeonServiceFactory 完整测试失败${NC}"
    fi
fi

echo -e "${YELLOW}========================================${NC}"
echo -e "${YELLOW}  运行 ThriftClientProxyBeanConfig 测试${NC}"
echo -e "${YELLOW}========================================${NC}"

# 首先尝试运行简化版本测试
echo "运行 ThriftClientProxyBeanConfig 简化测试（推荐）..."
mvn test -Dtest=ThriftClientProxyBeanConfigSimpleTest -q

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ ThriftClientProxyBeanConfig 简化测试通过${NC}"
else
    echo -e "${RED}❌ ThriftClientProxyBeanConfig 简化测试失败，尝试完整测试...${NC}"

    # 如果简化测试失败，尝试完整测试
    echo "运行 ThriftClientProxyBeanConfig 完整测试..."
    mvn test -Dtest=ThriftClientProxyBeanConfigTest -q

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ThriftClientProxyBeanConfig 完整测试通过${NC}"
    else
        echo -e "${RED}❌ ThriftClientProxyBeanConfig 完整测试失败${NC}"
    fi
fi

echo -e "${YELLOW}========================================${NC}"
echo -e "${YELLOW}  运行所有Factory相关测试${NC}"
echo -e "${YELLOW}========================================${NC}"

# 运行所有Factory相关测试
echo "运行所有Factory和Config相关测试..."
mvn test -Dtest="*Factory*Test,*Config*Test" -q

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 所有Factory相关测试通过${NC}"
else
    echo -e "${RED}❌ 部分Factory相关测试失败${NC}"
fi

echo -e "${YELLOW}========================================${NC}"
echo -e "${YELLOW}  测试详细信息${NC}"
echo -e "${YELLOW}========================================${NC}"

# 显示测试统计信息
echo "生成详细测试报告..."
mvn surefire-report:report -q

echo -e "${GREEN}测试运行完成！${NC}"
echo ""
echo "📊 测试报告位置:"
echo "   - HTML报告: target/site/surefire-report.html"
echo "   - XML报告: target/surefire-reports/"
echo ""
echo "🔍 查看具体测试结果:"
echo "   mvn test -Dtest=PigeonServiceFactoryTest#testCreatePigeonProxy_Success"
echo "   mvn test -Dtest=ThriftClientProxyBeanConfigTest#testCreateThriftProxy_WithAppKey"
echo ""
echo "📋 测试覆盖的主要场景:"
echo "   ✅ 代理创建和缓存机制"
echo "   ✅ 超时时间和默认值处理"
echo "   ✅ 不同连接模式配置"
echo "   ✅ 异常处理和边界条件"
echo "   ✅ 资源清理和销毁逻辑"
