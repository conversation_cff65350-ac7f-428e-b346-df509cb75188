# PigeonServiceFactory 和 ThriftClientProxyBeanConfig 单元测试

## 概述

本文档描述了为 `PigeonServiceFactory` 和 `ThriftClientProxyBeanConfig` 类创建的单元测试。

## 测试文件

### 1. PigeonServiceFactory 测试
**文件位置**: `predictor-service/src/test/java/com/sankuai/meishi/stgy/algoplatform/predictor/pigeon/PigeonServiceFactoryTest.java`

#### 测试覆盖场景：
- ✅ **正常创建代理** - 测试正常的Pigeon服务代理创建流程
- ✅ **缓存机制** - 测试从缓存获取已创建的代理
- ✅ **默认超时时间** - 测试超时时间为0或负数时使用默认值6000ms
- ✅ **空Cell处理** - 测试Cell为空字符串或null的情况
- ✅ **ServiceFactory异常** - 测试ServiceFactory.getService()抛出异常
- ✅ **多服务缓存** - 测试多个不同服务的缓存管理
- ✅ **相同服务复用** - 测试相同服务信息只创建一次代理

#### 关键测试点：
- 缓存key生成逻辑：`appKey_interfaceName_cell_timeout`
- 超时时间处理：`timeout <= 0` 时使用默认值6000
- InvokerConfig配置验证
- 静态缓存Map的正确使用

### 2. ThriftClientProxyBeanConfig 测试
**文件位置**: `predictor-service/src/test/java/com/sankuai/meishi/stgy/algoplatform/predictor/thrift/ThriftClientProxyBeanConfigTest.java`

#### 测试覆盖场景：
- ✅ **使用appKey创建** - 测试通过appKey创建Thrift代理
- ✅ **缓存机制** - 测试从缓存获取已创建的代理
- ✅ **IP+端口模式** - 测试通过IP和端口直连模式
- ✅ **appKey+端口模式** - 测试appKey配合端口的模式
- ✅ **默认超时时间** - 测试超时时间为null时使用默认值6000ms
- ✅ **端口处理** - 测试空字符串端口、无效端口等边界情况
- ✅ **销毁代理** - 测试通过key和ThriftServiceInfo销毁代理
- ✅ **销毁异常处理** - 测试销毁时抛出异常的处理
- ✅ **销毁所有代理** - 测试@PreDestroy方法
- ✅ **多服务缓存** - 测试多个不同服务的缓存管理
- ✅ **边界条件** - 测试所有字段为null的情况

#### 关键测试点：
- 缓存key生成逻辑：`appKey_interfaceName_cell_ip_port_timeOut`
- 超时时间处理：`timeOut == null` 时使用默认值6000
- 不同连接模式的配置逻辑
- 端口号解析和验证
- 资源清理和异常处理

## 运行测试

### 运行单个测试类
```bash
# 运行PigeonServiceFactory测试
mvn test -Dtest=PigeonServiceFactoryTest

# 运行ThriftClientProxyBeanConfig测试
mvn test -Dtest=ThriftClientProxyBeanConfigTest
```

### 运行特定测试方法
```bash
# 测试Pigeon代理创建
mvn test -Dtest=PigeonServiceFactoryTest#testCreatePigeonProxy_Success

# 测试Thrift代理缓存
mvn test -Dtest=ThriftClientProxyBeanConfigTest#testCreateThriftProxy_FromCache
```

### 运行所有Factory相关测试
```bash
mvn test -Dtest=*Factory*Test,*Config*Test
```

## 测试技术栈

- **JUnit 4**: 测试框架
- **Mockito**: Mock框架，用于模拟依赖对象
- **PowerMock**: 用于模拟静态方法（ServiceFactory）
- **反射**: 用于测试私有方法（destroyAll）

## 测试数据示例

### PigeonServiceInfo 示例
```java
PigeonServiceInfo serviceInfo = new PigeonServiceInfo();
serviceInfo.setAppKey("com.sankuai.test");
serviceInfo.setInterfaceName("com.test.Service");
serviceInfo.setMethodName("testMethod");
serviceInfo.setTimeout(5000);
serviceInfo.setCell("test-cell");
```

### ThriftServiceInfo 示例
```java
ThriftServiceInfo serviceInfo = new ThriftServiceInfo();
serviceInfo.setAppKey("com.sankuai.test");
serviceInfo.setInterfaceName("com.test.ThriftService");
serviceInfo.setMethodName("testMethod");
serviceInfo.setTimeOut(5000);
serviceInfo.setCell("test-cell");
serviceInfo.setIp("127.0.0.1");
serviceInfo.setPort("8080");
```

## 注意事项

### PigeonServiceFactory 测试注意点：
1. **静态缓存清理**: 每个测试前清空静态Map避免测试间干扰
2. **PowerMock使用**: 需要Mock ServiceFactory.getService()静态方法
3. **缓存key验证**: 重点验证缓存key的生成逻辑
4. **超时时间逻辑**: 验证默认超时时间的设置逻辑

### ThriftClientProxyBeanConfig 测试注意点：
1. **多种连接模式**: 测试appKey模式、IP+端口模式等不同配置
2. **资源清理**: 测试destroy方法和@PreDestroy注解方法
3. **异常处理**: 验证各种异常情况的处理逻辑
4. **端口解析**: 测试端口号的解析和验证逻辑

## Mock策略

### PigeonServiceFactory Mock:
- Mock `ServiceFactory.getService()` 静态方法
- Mock `GenericService` 返回对象
- 验证InvokerConfig的配置参数

### ThriftClientProxyBeanConfig Mock:
- Mock `ThriftClientProxy` 对象
- Mock `destroy()` 方法调用
- 验证ThriftClientProxy的配置参数

## 扩展建议

如需添加更多测试场景，可以考虑：
1. **并发测试** - 测试多线程同时创建代理的情况
2. **性能测试** - 测试大量代理创建和销毁的性能
3. **集成测试** - 结合Spring容器的集成测试
4. **配置验证** - 更详细的配置参数验证测试
5. **内存泄漏测试** - 验证代理对象的正确清理
