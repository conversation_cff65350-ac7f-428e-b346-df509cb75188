#!/bin/bash

# JavaBridge pigeonInvoke 和 thriftInvoke 方法测试运行脚本
# 使用方法: ./run_invoke_tests.sh

echo "开始运行 JavaBridge pigeonInvoke 和 thriftInvoke 方法的单元测试..."

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/../../../.." && pwd)
echo "项目根目录: $PROJECT_ROOT"

# 进入项目根目录
cd "$PROJECT_ROOT"

# 运行特定的测试方法
echo "运行 pigeonInvoke 相关测试..."
mvn test -Dtest=JavaBridgeTest#testPigeonInvoke_Success,JavaBridgeTest#testPigeonInvoke_InvalidJson,JavaBridgeTest#testPigeonInvoke_ServiceException,JavaBridgeTest#testPigeonInvoke_EmptyParams

echo "运行 thriftInvoke 相关测试..."
mvn test -Dtest=JavaBridgeTest#testThriftInvoke_Success,JavaBridgeTest#testThriftInvoke_InvalidJson,JavaBridgeTest#testThriftInvoke_ServiceException,JavaBridgeTest#testThriftInvoke_EmptyParams

# 如果创建了独立的测试文件，也运行它
if [ -f "src/test/java/com/sankuai/meishi/stgy/algoplatform/predictor/python/JavaBridgeInvokeTest.java" ]; then
    echo "运行独立的 JavaBridgeInvokeTest..."
    mvn test -Dtest=JavaBridgeInvokeTest
fi

echo "测试运行完成！"

# 生成测试报告
echo "生成测试报告..."
mvn surefire-report:report

echo "测试报告已生成，可在 target/site/surefire-report.html 查看"
