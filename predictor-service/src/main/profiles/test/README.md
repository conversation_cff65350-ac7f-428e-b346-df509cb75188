# JavaBridge pigeonInvoke 和 thriftInvoke 方法单元测试

## 概述

本目录包含了针对 `JavaBridge` 类中 `pigeonInvoke` 和 `thriftInvoke` 方法的完整单元测试。

## 文件结构

```
predictor-service/src/main/profiles/test/
├── JavaBridgeInvokeTest.java          # 原始测试文件（已移动到标准测试目录）
├── run_invoke_tests.sh                # 测试运行脚本
├── test-config.md                     # 详细测试配置说明
└── README.md                          # 本文件
```

## 测试文件位置

实际的测试文件位于标准的Maven测试目录中：
- `predictor-service/src/test/java/com/sankuai/meishi/stgy/algoplatform/predictor/python/JavaBridgeTest.java`
- `predictor-service/src/test/java/com/sankuai/meishi/stgy/algoplatform/predictor/python/JavaBridgeInvokeTest.java`

## 快速开始

### 1. 运行所有相关测试
```bash
cd /Users/<USER>/IdeaProjects/algoplat-predictor
mvn test -Dtest=JavaBridge*Test
```

### 2. 运行特定方法的测试
```bash
# 测试 pigeonInvoke 方法
mvn test -Dtest=JavaBridgeInvokeTest#testPigeonInvoke_Success

# 测试 thriftInvoke 方法
mvn test -Dtest=JavaBridgeInvokeTest#testThriftInvoke_Success
```

### 3. 使用提供的脚本
```bash
chmod +x predictor-service/src/main/profiles/test/run_invoke_tests.sh
./predictor-service/src/main/profiles/test/run_invoke_tests.sh
```

## 测试覆盖的场景

### pigeonInvoke 方法测试
- ✅ 正常调用成功
- ✅ JSON格式错误处理
- ✅ 创建代理失败处理
- ✅ 服务调用异常处理
- ✅ 空参数列表处理
- ✅ null参数边界测试
- ✅ 参数类型和值不匹配
- ✅ 返回null结果处理

### thriftInvoke 方法测试
- ✅ 正常调用成功
- ✅ JSON格式错误处理
- ✅ 创建代理失败处理
- ✅ 获取服务对象失败处理
- ✅ 服务调用异常处理
- ✅ 空参数列表处理
- ✅ null参数边界测试
- ✅ 参数类型和值不匹配
- ✅ 返回null结果处理

## 测试技术栈

- **JUnit 4**: 测试框架
- **Mockito**: Mock框架
- **PowerMock**: 静态方法Mock
- **FastJSON**: JSON处理

## 示例测试数据

### PigeonServiceInfo 示例
```json
{
  "appKey": "com.sankuai.test",
  "interfaceName": "com.test.Service", 
  "methodName": "testMethod",
  "timeout": 5000
}
```

### ThriftServiceInfo 示例
```json
{
  "appKey": "com.sankuai.test",
  "interfaceName": "com.test.ThriftService",
  "methodName": "testMethod", 
  "timeOut": 5000
}
```

## 注意事项

1. 测试使用了大量Mock对象，确保测试环境的隔离性
2. 重点测试了异常处理和边界条件
3. 使用PowerMock来模拟SpringContextUtils静态方法
4. 测试覆盖了方法的所有主要执行路径

## 生成测试报告

```bash
mvn surefire-report:report
```

报告将生成在 `target/site/surefire-report.html`

## 扩展测试

如需添加更多测试场景，可以考虑：
- 不同参数类型的组合测试
- 超时场景测试
- 并发调用测试
- 性能测试
- 集成测试（需要真实服务环境）
